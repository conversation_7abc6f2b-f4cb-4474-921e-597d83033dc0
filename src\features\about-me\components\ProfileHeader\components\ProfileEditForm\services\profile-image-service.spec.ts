import { profileImageService } from './profileImageService';

describe('profileImageService', () => {
  describe('uploadImage', () => {
    it('should successfully process valid image file', async () => {
      // Arrange
      const validImageFile = new File(['test-image'], 'test.jpg', { type: 'image/jpeg' });
      const mockBase64 = 'data:image/jpeg;base64,dGVzdC1pbWFnZQ==';

      // Mock FileReader
      const mockFileReader: any = {
        readAsDataURL: jest.fn(),
        onload: null,
        result: mockBase64,
        EMPTY: 0,
        LOADING: 1,
        DONE: 2,
      };

      // Mock FileReader constructor
      const mockFileReaderConstructor = jest.fn(() => mockFileReader) as any;
      mockFileReaderConstructor.EMPTY = 0;
      mockFileReaderConstructor.LOADING = 1;
      mockFileReaderConstructor.DONE = 2;
      window.FileReader = mockFileReaderConstructor;

      // Simulate FileReader load
      setTimeout(() => {
        mockFileReader.onload();
      }, 0);

      // Act
      const result = await profileImageService.uploadImage(validImageFile);

      // Assert
      expect(result).toEqual({ imageData: mockBase64 });
    });

    it('should reject non-image files', async () => {
      // Arrange
      const nonImageFile = new File(['test-doc'], 'test.pdf', { type: 'application/pdf' });

      // Act & Assert
      await expect(profileImageService.uploadImage(nonImageFile)).rejects.toThrow('Failed to process image upload');
    });

    it('should reject files larger than 5MB', async () => {
      // Arrange
      const largeImageFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });

      // Act & Assert
      await expect(profileImageService.uploadImage(largeImageFile)).rejects.toThrow('Failed to process image upload');
    });

    it('should handle FileReader errors', async () => {
      // Arrange
      const validImageFile = new File(['test-image'], 'test.jpg', { type: 'image/jpeg' });

      // Mock FileReader with error
      const mockFileReader: any = {
        readAsDataURL: jest.fn(),
        onerror: null,
        EMPTY: 0,
        LOADING: 1,
        DONE: 2,
      };

      // Mock FileReader constructor
      const mockFileReaderConstructor = jest.fn(() => mockFileReader) as any;
      mockFileReaderConstructor.EMPTY = 0;
      mockFileReaderConstructor.LOADING = 1;
      mockFileReaderConstructor.DONE = 2;
      window.FileReader = mockFileReaderConstructor;

      // Simulate FileReader error
      setTimeout(() => {
        mockFileReader.onerror(new Error('FileReader error'));
      }, 0);

      // Act & Assert
      await expect(profileImageService.uploadImage(validImageFile)).rejects.toThrow('Failed to process image upload');
    });
  });

  describe('removeImage', () => {
    it('should successfully remove image', async () => {
      // Act
      const result = await profileImageService.removeImage();

      // Assert
      expect(result).toEqual({ success: true });
    });

    it('should handle removal errors', async () => {
      // Arrange
      const error = new Error('API Error');
      jest.spyOn(Promise, 'resolve').mockImplementationOnce(() => Promise.reject(error));

      // Act & Assert
      await expect(profileImageService.removeImage()).rejects.toThrow('Failed to remove profile image');
    });
  });
});
