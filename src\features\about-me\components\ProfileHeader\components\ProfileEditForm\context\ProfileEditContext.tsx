import React, { createContext, useContext, ReactNode, useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useFormWrapper } from '@components/forms/hooks/useFormWrapper';
import { ProfileEditFormData, profileEditFormSchema } from '../profile-edit-form-schema';
import { useProfileEditForm } from '../hooks/useProfileEditForm';

interface ProfileEditState {
  isLoading: boolean;
  error: string | null;
  photoData: string | null;
}

interface ProfileEditContextValue {
  state: ProfileEditState;
  methods?: UseFormReturn<ProfileEditFormData>;
  actions: {
    handlePhotoUpload?: (file: File) => Promise<string>;
    handlePhotoRemove?: () => Promise<void>;
    submitProfile?: (data: ProfileEditFormData) => Promise<void>;
  };
  actionButtons?: any; // We'll get this from useFormWrapper
  handleSubmit?: (e?: React.BaseSyntheticEvent) => Promise<void>;
}

const initialState: ProfileEditState = {
  isLoading: false,
  error: null,
  photoData: null,
};

const initialContext: ProfileEditContextValue = {
  state: initialState,
  actions: {},
};

const ProfileEditContext = createContext<ProfileEditContextValue>(initialContext);

interface ProfileEditProviderProps {
  children: ReactNode;
  initialData?: ProfileEditFormData;
  onCancel?: () => void;
}

/**
 * Provider component for profile edit functionality
 * Currently a minimal implementation that will be expanded incrementally
 */

export const ProfileEditProvider: React.FC<ProfileEditProviderProps> = ({ children, initialData, onCancel }) => {
  const defaultFormValues: ProfileEditFormData = {
    profilePhoto: initialData?.profilePhoto || '',
    biography: initialData?.biography || '',
    pronouns: initialData?.pronouns || '',
  };

  // Placeholder for the submit action
  const onSubmitRef = React.useRef<(data: ProfileEditFormData) => Promise<void>>();

  const { methods, actionButtons, handleSubmit } = useFormWrapper({
    id: 'profile-edit',
    schema: profileEditFormSchema,
    defaultValues: defaultFormValues,
    onSubmit: (data) => onSubmitRef.current?.(data),
    onCancel,
    submitLabel: 'Save changes',
    cancelLabel: 'Cancel',
    isLoading: false, // This will be managed by the hook's state
  });

  const { state, actions } = useProfileEditForm(methods);

  // Assign the real submit action
  onSubmitRef.current = actions.submitProfile;

  const value = useMemo<ProfileEditContextValue>(
    () => ({
      state,
      methods,
      actions,
      actionButtons,
      handleSubmit,
    }),
    [state, methods, actions, actionButtons, handleSubmit],
  );

  return <ProfileEditContext.Provider value={value}>{children}</ProfileEditContext.Provider>;
};

/**
 * Hook to access profile edit context
 * @returns ProfileEditContextValue
 */
export const useProfileEditContext = () => {
  const context = useContext(ProfileEditContext);
  if (!context) {
    throw new Error('useProfileEditContext must be used within a ProfileEditProvider');
  }
  return context;
};

export type { ProfileEditContextValue, ProfileEditState };
