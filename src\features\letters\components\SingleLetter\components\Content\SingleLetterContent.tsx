import React from 'react';
import { Image } from '@ceridianhcm/components';
import { Container } from '@components';
import { usePageShell } from '@components/PageShell';
import { Wrapper } from '@components/Wrapper';
import { IEmployeeLetter } from '@/models';
import { sanitizeHtml } from '@utils/sanitizeUtils';
import { getLetterDimensions } from '../single-letter.constants';

import './single-letter-content.scss';

export interface SingleLetterContentProps {
  id: string;
  testId: string;
  selectedLetter: IEmployeeLetter;
}

export const SingleLetterContent: React.FC<SingleLetterContentProps> = ({ id, testId, selectedLetter }) => {
  const { breakpoint } = usePageShell();
  const dimensions = getLetterDimensions(breakpoint);

  const sanitizedHtml = sanitizeHtml(selectedLetter.SubmittedLetterHtml || '');

  return (
    <Wrapper
      testId={`${id}-container-panel-wrapper`}
      alignItems="center"
      minWidth={dimensions.containerWidth}
      maxWidth={dimensions.containerWidth}
    >
      <Container id={`${id}-container-panel`} ariaLabel="Single Letter Content">
        <Container.Body testId={`${testId}-container-body`} className="single-letter-content-container">
          <Wrapper
            testId={`${id}-content-section-panel-wrapper-inner`}
            alignItems="start"
            minWidth={dimensions.contentWidth}
            maxWidth={dimensions.contentWidth}
            verticalPadding={dimensions.spacing}
            gap={dimensions.gap}
            className="single-letter-content-wrapper"
          >
            <div className="company-logo-wrapper">
              <Image id="dayforce-logo" src="/assets/images/dayforce-logo.svg" alt="dayforce logo" />
            </div>
            <div
              dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
              data-testid={`${testId}-letter-content`}
              className="single-letter-html-content"
            />
          </Wrapper>
        </Container.Body>
      </Container>
    </Wrapper>
  );
};
